// 测试同步API的脚本
const https = require('https');

// 测试API健康检查
function testHealthCheck() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.dznovel.top',
      port: 443,
      path: '/api/health?_api_path=health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log('健康检查响应状态:', res.statusCode);
        console.log('健康检查响应内容:', data);
        resolve({ statusCode: res.statusCode, data: data });
      });
    });

    req.on('error', (error) => {
      console.error('健康检查请求失败:', error);
      reject(error);
    });

    req.setTimeout(10000, () => {
      console.error('健康检查请求超时');
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

// 测试同步上传API（需要有效的token）
function testSyncUpload(token) {
  return new Promise((resolve, reject) => {
    const testData = {
      data: {
        userSettings: {
          testKey: 'testValue',
          timestamp: new Date().toISOString()
        }
      },
      timestamp: new Date().toISOString(),
      dataType: 'userSettings'
    };

    const postData = JSON.stringify(testData);

    const options = {
      hostname: 'api.dznovel.top',
      port: 443,
      path: '/api/sync/upload?_api_path=sync/upload',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log('同步上传响应状态:', res.statusCode);
        console.log('同步上传响应内容:', data);
        resolve({ statusCode: res.statusCode, data: data });
      });
    });

    req.on('error', (error) => {
      console.error('同步上传请求失败:', error);
      reject(error);
    });

    req.setTimeout(30000, () => {
      console.error('同步上传请求超时');
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.write(postData);
    req.end();
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试同步API...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查API...');
    const healthResult = await testHealthCheck();
    
    if (healthResult.statusCode === 200) {
      console.log('✅ 健康检查通过\n');
    } else {
      console.log('❌ 健康检查失败\n');
      return;
    }

    // 注意：这里需要一个有效的token来测试同步上传
    // 在实际使用中，你需要先登录获取token
    console.log('2. 测试同步上传API...');
    console.log('⚠️  需要有效的认证token才能测试同步上传功能');
    console.log('   请在Flutter应用中查看上传日志以验证修复效果\n');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
runTests();
