# 分块上传实现说明

## 🎯 问题分析

通过网络搜索发现，CloudBase云函数HTTP请求体有严格的大小限制：
- **文本类型（JSON）**: 100KB
- **其他类型**: 6MB

你的小说数据是1MB的JSON格式，超过了100KB的文本限制，所以被服务器拒绝连接。

## 🔧 解决方案

实现了按章节分块上传的策略，将大数据分解为小块进行传输。

### 核心实现

#### 1. 数据大小检测
```dart
// 检查数据大小，如果超过80KB则使用分块上传
final dataString = jsonEncode({
  'data': {dataType: data},
  'timestamp': DateTime.now().toIso8601String(),
  'dataType': dataType
});
final dataSizeKB = dataString.length / 1024;

if (dataSizeKB > 80) {
  print('📦 数据过大，使用分块上传策略');
  // 使用分块上传
}
```

#### 2. 按章节分块策略
- **每3个章节为一块**
- **每本小说独立处理**
- **包含分块信息用于服务端重组**

```dart
// 按每3个章节分块
const chunkSize = 3;
final totalChunks = (chapters.length / chunkSize).ceil();

for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
  final startIndex = chunkIndex * chunkSize;
  final endIndex = (startIndex + chunkSize).clamp(0, chapters.length);
  final chapterChunk = chapters.sublist(startIndex, endIndex);
  
  // 创建包含章节块的小说数据
  final novelChunk = Map<String, dynamic>.from(novel);
  novelChunk['chapters'] = chapterChunk;
  novelChunk['chunkInfo'] = {
    'novelIndex': novelIndex,
    'chunkIndex': chunkIndex,
    'totalChunks': totalChunks,
    'chapterRange': '${startIndex + 1}-$endIndex'
  };
}
```

#### 3. 单块上传
```dart
Future<bool> _uploadSingleChunk(
    dynamic chunkData, String dataType, String token, int chunkIndex, int totalChunks) async {
  final response = await http.post(
    Uri.parse(ApiConfig.getEndpoint('syncUpload')),
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    },
    body: jsonEncode({
      'data': {dataType: chunkData},
      'timestamp': DateTime.now().toIso8601String(),
      'dataType': dataType,
      'chunkInfo': {
        'chunkIndex': chunkIndex,
        'totalChunks': totalChunks,
        'isChunked': true
      }
    }),
  ).timeout(Duration(seconds: 30));
}
```

## 📊 分块策略详情

### 小说数据分块
1. **按小说遍历**: 逐本处理每个小说
2. **按章节分块**: 每3个章节为一个上传块
3. **保持结构**: 每个块包含完整的小说元数据
4. **添加标识**: 包含分块信息便于服务端重组

### 分块信息结构
```json
{
  "chunkInfo": {
    "novelIndex": 0,        // 小说在列表中的索引
    "chunkIndex": 0,        // 当前块的索引
    "totalChunks": 5,       // 该小说的总块数
    "chapterRange": "1-3",  // 章节范围
    "isChunked": true       // 标识这是分块数据
  }
}
```

## 🚀 优势特点

### 1. 突破大小限制
- 每个块都小于100KB限制
- 支持任意大小的小说数据

### 2. 保持数据完整性
- 按章节自然分割，不破坏内容结构
- 包含完整的小说元数据

### 3. 错误恢复
- 单块失败不影响其他块
- 可以重试失败的块

### 4. 进度可视化
```
📚 开始按章节分块上传小说数据，共 19 本小说
📖 处理小说: 赛博朋克：2075 (1/19)
📄 小说共有 15 个章节
📦 上传章节块 1/5 (章节 1-3)
✅ 数据块上传成功: 1/5
📦 上传章节块 2/5 (章节 4-6)
✅ 数据块上传成功: 2/5
```

## 🔧 服务端适配

服务端需要识别分块数据并进行重组：

```javascript
// 检查是否为分块数据
if (chunkInfo && chunkInfo.isChunked) {
  // 处理分块数据
  await handleChunkedData(userId, data, chunkInfo);
} else {
  // 处理完整数据
  await handleCompleteData(userId, data);
}
```

## 📈 性能优化

### 1. 请求间隔
- 每个块之间延迟500ms，避免请求过于频繁
- 减少服务器压力

### 2. 超时设置
- 单块上传超时30秒（比完整上传的60秒更短）
- 更快的错误检测和重试

### 3. 并发控制
- 串行上传，确保数据顺序
- 避免并发请求导致的服务器压力

## 🎯 预期效果

实现分块上传后：
1. **小说数据上传成功率大幅提升**
2. **不再出现"远程主机强制关闭连接"错误**
3. **支持任意大小的小说数据**
4. **提供详细的上传进度信息**

## 🧪 测试建议

1. **测试小数据**: 确保小于80KB的数据仍使用原始方法
2. **测试大数据**: 验证超过80KB的数据使用分块上传
3. **测试网络中断**: 验证单块失败时的错误处理
4. **测试数据完整性**: 确保分块上传后数据完整无损
