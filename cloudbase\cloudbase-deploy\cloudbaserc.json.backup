{"envId": "novel-app-2gywkgnn15cbd6a8", "functionRoot": "./", "functions": [{"name": "novel-app-api", "timeout": 60, "envVariables": {"NODE_ENV": "production", "JWT_SECRET": "your-production-jwt-secret-key-2024", "ADMIN_USERNAME": "admin", "ADMIN_PASSWORD": "your-secure-admin-password"}, "runtime": "Nodejs18.15", "memorySize": 512, "handler": "index.main"}], "framework": {"name": "novel-app-backend", "plugins": {"function": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./novel-app-api", "functions": [{"name": "novel-app-api", "timeout": 60, "envVariables": {"NODE_ENV": "production", "JWT_SECRET": "your-production-jwt-secret-key-2024", "ADMIN_USERNAME": "admin", "ADMIN_PASSWORD": "your-secure-admin-password"}, "runtime": "Nodejs18.15", "memorySize": 512, "handler": "index.main"}]}}, "database": {"use": "@cloudbase/framework-plugin-database", "inputs": {"collections": [{"name": "users", "indexes": [{"keys": [{"username": 1}], "options": {"unique": true}}, {"keys": [{"phoneNumber": 1}], "options": {"unique": true}}]}, {"name": "novels", "indexes": [{"keys": [{"userId": 1, "createdAt": -1}]}]}, {"name": "memberCodes", "indexes": [{"keys": [{"code": 1}], "options": {"unique": true}}]}]}}}}}