// 创建缺失的数据库集合
const cloudbase = require('@cloudbase/node-sdk');

// 初始化CloudBase
const app = cloudbase.init({
  env: 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

async function createMissingCollections() {
  try {
    console.log('🚀 开始创建缺失的数据库集合...');

    // 创建 user_data_chunks 集合
    console.log('📦 创建 user_data_chunks 集合...');
    try {
      await db.createCollection('user_data_chunks');
      console.log('✅ user_data_chunks 集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  user_data_chunks 集合已存在');
      } else {
        console.error('❌ 创建 user_data_chunks 集合失败:', error);
      }
    }

    // 验证集合是否存在
    console.log('\n🔍 验证集合状态...');
    
    // 测试写入一条记录到 user_data_chunks
    try {
      const testRecord = {
        userId: 'test_user',
        chunkIndex: 0,
        totalChunks: 1,
        chunkData: 'test_data',
        timestamp: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        chunkSize: 9
      };
      
      const result = await db.collection('user_data_chunks').add(testRecord);
      console.log('✅ user_data_chunks 集合写入测试成功:', result.id);
      
      // 删除测试记录
      await db.collection('user_data_chunks').doc(result.id).remove();
      console.log('✅ 测试记录已清理');
      
    } catch (error) {
      console.error('❌ user_data_chunks 集合测试失败:', error);
    }

    // 测试 sync_records 集合
    try {
      const testSyncRecord = {
        userId: 'test_user',
        timestamp: new Date().toISOString(),
        totalSize: 100,
        totalChunks: 1,
        status: 'test',
        syncTime: new Date().toISOString()
      };
      
      const result = await db.collection('sync_records').add(testSyncRecord);
      console.log('✅ sync_records 集合写入测试成功:', result.id);
      
      // 删除测试记录
      await db.collection('sync_records').doc(result.id).remove();
      console.log('✅ 测试记录已清理');
      
    } catch (error) {
      console.error('❌ sync_records 集合测试失败:', error);
    }

    console.log('\n🎉 数据库集合检查和创建完成！');
    
  } catch (error) {
    console.error('❌ 创建集合过程中出现错误:', error);
  }
}

// 运行创建函数
createMissingCollections();
